<?php
declare(strict_types=1);

namespace App\Service\Clue;

use App\Models\Citui\App;
use App\Service\BaseService;
use App\Exceptions\MyException;
use App\Models\Citui\WaterClues;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class ClueService extends BaseService
{

    /**
     * 提交线索
     * @param array $data
     * @return array
     * @throws \Exception
     */
    public function submitClue(array $data): array
    {
        try {
            DB::beginTransaction();

            // 获取当前用户信息
            $user = $this->request->attributes->get('user');
            if (!$user) {
                throw new MyException('用户未登录');
            }
            $userId = $user['id'];

            // 验证APP是否存在
            $app = App::find($data['app_id']);
            if (!$app) {
                throw new MyException('选择的APP不存在');
            }

            // 处理单包大小映射
            $packageMoneyMap = [
                '0.1-0.29' => 1,
                '0.3-0.49' => 2,
                '0.5-0.99' => 3,
                '1以上' => 4
            ];

            $packageMoney = $packageMoneyMap[$data['package_amount']] ?? 1;

            // 生成线索标题（后端自动拼接）
            $clueTitle = $app->app_name . ' - 放水线索 - ' . date('Y-m-d H:i');

            // 处理放水时间
            $clueTime = now(); // 默认当前时间
            try{
                if (!empty($data['release_time'])) {
                    // 前端传递的是毫秒时间戳，需要转换为秒
                    $timestamp = intval($data['release_time']);
                    if ($timestamp > 0) {
                        // 判断是否为毫秒时间戳：如果时间戳长度为13位，则为毫秒时间戳
                        if (strlen((string)$timestamp) === 13) {
                            $timestamp = intval($timestamp / 1000);
                        }
                        // 验证时间戳是否合理（不能是未来太远的时间，也不能是过去太久的时间）
                        $currentTime = time();
                        if ($timestamp > 0 && $timestamp >= ($currentTime - 86400 * 30) && $timestamp <= ($currentTime + 86400)) {
                            $clueTime = date('Y-m-d H:i:s', $timestamp);
                        }
                    }
                }
            }catch(\Exception $e){

            }
            

            // 准备插入数据
            $clueData = [
                'app_id' => intval($data['app_id']),
                'user_id' => $userId,
                'clue_title' => $clueTitle,
                'nick_name' => trim($data['nick_name']),
                'clue_content' => trim($data['clue_description']),
                'clue_time' => $clueTime,
                'clue_money' => floatval($data['clue_money'] ?? 0.00),
                'package_money' => $packageMoney,
                'device_model' => trim($data['device_model']),
                'pic_tixian' => trim($data['pic_tixian'] ?? ''),
                'pic_daozhang' => trim($data['pic_daozhang'] ?? ''),
                'status' => 1, // 待审核
                'submitted_at' => now(),
                'created_at' => now(),
                'updated_at' => now()
            ];

            // 创建线索记录
            $clue = WaterClues::create($clueData);

            DB::commit();

            return [
                'clue_id' => $clue->id
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            throw new MyException('提交失败，请重试');
        }
    }

    /**
     * 获取用户的线索列表
     * @return array
     */
    public function getUserClues(): array
    {
        // 获取当前用户信息
        $user = $this->request->attributes->get('user');
        if (!$user) {
            throw new MyException('用户未登录');
        }
        $userId = $user['id'];
        
        $query = WaterClues::query()
            ->with(['app:id,app_name'])
            ->where('status',1)
            ->where('user_id', $userId)
            ->orderBy('id', 'desc');

        $total = $query->count();
        $clues = $query->forPage($this->page_no, $this->per_page)->get();

        $clueList = $clues->map(function ($clue) {
            return [
                'id' => $clue->id,
                'app_name' => $clue->app->app_name ?? '未知APP',
                'clue_title' => $clue->clue_title,
                'clue_money' => (float) $clue->clue_money,
                'package_money_text' => $clue->package_money_text,
                'device_model' => $clue->device_model,
                'status' => $clue->status,
                'status_text' => $this->getStatusText($clue->status),
                'submitted_at' => $clue->submitted_at ? $clue->submitted_at->format('Y-m-d H:i:s') : '',
                'approved_at' => $clue->approved_at ? $clue->approved_at->format('Y-m-d H:i:s') : '',
                'created_at' => $clue->created_at->format('Y-m-d H:i:s')
            ];
        })->toArray();

        return [
            'list' => $clueList,
            'page' => $this->page_no,
            'page_size' => $this->per_page,
            'total' => $total,
            'has_more' => ($this->page_no * $this->per_page) < $total
        ];
    }

    /**
     * 获取线索列表（公开接口，无需登录）
     * @return array
     */
    public function getClueList(): array
    {
        // 构建查询
        $query = WaterClues::query()
            ->with(['app' => function($query) {
                $query->select('id', 'app_name', 'logo_url', 'category_id', 'rating', 'download_count', 'updated_at');
            }])
            ->where('status', 1); // 只显示审核通过的线索

        // 处理搜索条件
        $search = $this->request->get('search');
        if (!empty($search)) {
            $query->whereHas('app', function($q) use ($search) {
                $q->where('app_name', 'like', '%' . $search . '%');
            });
        }

        // 排序：按创建时间倒序
        $query->orderBy('id', 'desc');

        // 获取总数
        $total = $query->count();

        // 获取分页数据
        $clues = $query->forPage($this->page_no, $this->per_page)->get();

        $clueList = $clues->map(function ($clue) {
            $app = $clue->app;
            if (!$app) {
                return null; // 跳过没有关联APP的记录
            }

            return [
                'id' => $clue->id,
                'appName' => $app->app_name,
                'type' => $this->getAppType($app->category_id),
                'logoUrl' => uploadFilePathNoPre($app->logo_url),
                'rating' => (float) ($app->rating ?? 4.0),
                'downloadCount' => $app->download_count ?? 0,
                'submitTime' => $clue->created_at->format('m-d H:i'),
                'tags' => $this->generateClueTags($clue),
                'feedback' => [
                    'highlight' => $this->generateHighlight($clue),
                    'content' => $clue->clue_content
                ],
                'submitter' => [
                    'name' => $clue->nick_name ?? '平台用户',
                    'device' => $clue->device_model ?? '移动设备'
                ]
            ];
        })->filter()->values()->toArray(); // 过滤掉null值并重新索引

        return [
            'list' => $clueList,
            'page' => $this->page_no,
            'page_size' => $this->per_page,
            'total' => $total,
            'has_more' => ($this->page_no * $this->per_page) < $total
        ];
    }

    /**
     * 根据分类ID获取APP类型
     * @param int $categoryId
     * @return string
     */
    private function getAppType(int $categoryId): string
    {
        $typeMap = [
            1 => 'coin',    // 合成游戏
            2 => 'video',   // 短剧
            3 => 'book',    // 阅读
            4 => 'cash',    // 走路
            5 => 'game',    // 答题
            6 => 'coin',    // 其它
        ];

        return $typeMap[$categoryId] ?? 'coin';
    }

    /**
     * 生成线索标签
     * @param WaterClues $clue
     * @return array
     */
    private function generateClueTags(WaterClues $clue): array
    {
        $tags = [];

        // 热门标签
        $tags[] = ['text' => '热门', 'type' => 'red', 'emoji' => '🔥'];

        // 单包大小标签
        $packageText = $clue->package_money_text;
        if ($packageText) {
            $tags[] = ['text' => $packageText, 'type' => 'amber', 'emoji' => '⚡'];
        }

        // 放水金额标签
        if ($clue->clue_money > 0) {
            $tags[] = ['text' => '放水￥' . $clue->clue_money, 'type' => 'green', 'emoji' => ''];
        }

        // 新线索标签（24小时内）
        if ($clue->created_at->diffInHours(now()) <= 24) {
            $tags[] = ['text' => '新线索', 'type' => 'blue', 'emoji' => ''];
        }

        return array_slice($tags, 0, 4); // 最多显示4个标签
    }

    /**
     * 生成高亮文本
     * @param WaterClues $clue
     * @return string
     */
    private function generateHighlight(WaterClues $clue): string
    {
        if ($clue->clue_money > 0) {
            return "今日放水{$clue->clue_money}元";
        }

        return "最新放水线索";
    }

    /**
     * 获取状态文本
     * @param int $status
     * @return string
     */
    private function getStatusText(int $status): string
    {
        $statusMap = [
            1 => '审核通过',
            2 => '待审核',
            3 => '审核拒绝',
            4 => '已过期'
        ];

        return $statusMap[$status] ?? '未知状态';
    }
}
