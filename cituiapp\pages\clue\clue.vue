<template>
	<view class="page-container">
		<!-- 固定顶部区域 -->
		<view class="fixed-header">
			<!-- 搜索框 -->
			<view class="search-section">
				<view class="search-container">
					<input
						v-model="searchValue"
						type="text"
						placeholder="搜索APP"
						class="search-input"
						@confirm="handleSearch"
						@input="handleSearchInput"
					/>
					<view v-if="!isSearching" class="search-icon" @click="handleSearch">
						<view class="icon-search"></view>
					</view>
					<view v-else class="clear-icon" @click="clearSearch">
						<view class="icon-clear">×</view>
					</view>
				</view>
			</view>
			
			<!-- 标签导航 -->
			<view class="tab-section">
				<view class="tab-container">
					<view 
						v-for="(tab, index) in tabs" 
						:key="index"
						class="tab-item"
						:class="{ 'active': activeTab === index }"
						@click="switchTab(index)"
					>
						<text class="tab-text">{{ tab.name }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 内容区域 -->
		<view class="content-container">
			<scroll-view 
				class="scroll-container"
				scroll-y
				refresher-enabled
				:refresher-triggered="isRefreshing"
				@refresherrefresh="onRefresh"
				@refresherrestore="onRestore"
				@scrolltolower="onLoadMore"
				lower-threshold="100"
			>
				<view class="content-inner">
					<!-- 提交按钮 - 置顶 -->
					<view class="submit-section">
						<button class="submit-btn" @click="handleSubmitClue">
							<text class="submit-icon">⊕</text>
							<text class="submit-text">提交放水线索赚取积分</text>
						</button>
					</view>

					<!-- 标题区域 -->
					<view v-if="isSearching" class="section-title">
						<text class="title-text">搜索结果：{{ searchKeyword }}</text>
					</view>
					
					<!-- 放水线索时间线 -->
					<view class="timeline-container">
						<view 
							v-for="(item, index) in clueList" 
							:key="item.id"
							class="timeline-item"
							@click="handleClueClick(item)"
						>
							<!-- 时间线左侧图标 -->
							<view class="timeline-left">
								<view class="timeline-icon">
									<text class="water-icon">💧</text>
								</view>
								<!-- 连接线 - 最后一项不显示 -->
								<view 
									v-if="index < clueList.length - 1" 
									class="timeline-line"
								></view>
							</view>
							
							<!-- 时间线右侧内容 -->
							<view class="timeline-right">
								<!-- 时间戳 -->
								<view class="timeline-time">
									<text class="time-text">{{ item.submitTime }}</text>
								</view>
								
								<!-- 线索卡片 -->
								<view class="clue-card" :class="getCardGradientClass(index)">
									<!-- 卡片头部 -->
									<view class="card-header">
										<view class="app-info">
											<view class="app-icon" :class="getIconClass(item.type)">
												<text class="icon-symbol">{{ getIconSymbol(item.type) }}</text>
											</view>
											<text class="app-name">{{ item.appName }}</text>
										</view>
										<view class="app-stats">
											<view class="rating">
												<text class="star-icon">⭐</text>
												<text class="rating-text">{{ item.rating }}</text>
											</view>
											<view class="download-count">
												<text class="download-icon">⬇</text>
												<text class="count-text">{{ item.downloadCount }}次</text>
											</view>
										</view>
									</view>
									
									<!-- 标签区域 -->
									<view class="tags-container">
										<view 
											v-for="tag in item.tags" 
											:key="tag.text"
											class="tag"
											:class="getTagClass(tag.type)"
										>
											<text class="tag-text">{{ tag.emoji }}{{ tag.text }}</text>
										</view>
									</view>
									
									<!-- 用户反馈区域 -->
									<view class="feedback-section">
										<text class="feedback-label">用户反馈：</text>
										<text class="feedback-highlight">{{ item.feedback.highlight }}</text>
										<text class="feedback-content">{{ item.feedback.content }}</text>
										<view class="feedback-footer">
											<text class="submitter-info">提交人：{{ item.submitter.nick_name }} [{{ item.submitter.device }}]</text>
										</view>
									</view>
									
									<!-- 卡片底部按钮 -->
									<view class="card-footer">
										<view class="action-btn" @click.stop="handleDownload(item)">
											<text class="btn-text">下载赚钱</text>
										</view>
										<view class="detail-btn" @click.stop="handleDetail(item)">
											<text class="detail-text">查看详情</text>
											<text class="arrow">›</text>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
					
					<!-- 加载更多提示 -->
					<view class="load-more-container" v-if="showLoadMore">
						<u-loadmore 
							:status="loadStatus" 
							:load-text="loadText"
							margin-top="20"
							margin-bottom="20"
						/>
					</view>
					
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				searchValue: '',
				isSearching: false, // 是否处于搜索状态
				searchKeyword: '', // 当前搜索关键词
				activeTab: 0,
				tabs: [
					{ name: '最新线索' }
				],
				isRefreshing: false,
				showLoadMore: false,
				loadStatus: 'loadmore',
				loadText: {
					loadmore: '点击或上拉加载更多',
					loading: '正在加载...',
					nomore: '没有更多了'
				},
				currentPage: 1,
				pageSize: 10,
				clueList: [
					{
						id: 1,
						appName: '金银合合',
						type: 'coin',
						rating: 4.7,
						downloadCount: 2360,
						submitTime: '2025-05-13 10:23',
						tags: [
							{ text: '自动', type: 'red', emoji: '🔥' },
							{ text: '$0.1起提', type: 'amber', emoji: '⚡' },
							{ text: '新人$0.25', type: 'green', emoji: '' },
							{ text: '顶包￥2', type: 'blue', emoji: '' }
						],
						feedback: {
							highlight: '今日放水1.2元',
							content: '，新人秒到账，实名后提现速度非常快！'
						},
						submitter: {
							name: '用户8273',
							device: '华为mate40 Pro'
						}
					},
					{
						id: 2,
						appName: '趣步多多',
						type: 'cash',
						rating: 4.5,
						downloadCount: 1860,
						submitTime: '2025-05-12 18:45',
						tags: [
							{ text: '高佣', type: 'red', emoji: '🔥' },
							{ text: '$0.5起提', type: 'amber', emoji: '⚡' },
							{ text: '走路赚钱', type: 'green', emoji: '' },
							{ text: '首单￥3', type: 'blue', emoji: '' }
						],
						feedback: {
							highlight: '放水0.8元',
							content: '，到账速度快，今天特别活动双倍奖励！'
						},
						submitter: {
							name: '用户5146',
							device: '小米13'
						}
					},
					{
						id: 3,
						appName: '短剧星球',
						type: 'video',
						rating: 4.9,
						downloadCount: 3560,
						submitTime: '2025-05-12 15:10',
						tags: [
							{ text: '爆款', type: 'red', emoji: '🔥' },
							{ text: '秒提现', type: 'amber', emoji: '⚡' },
							{ text: '新人$1.0', type: 'green', emoji: '' },
							{ text: '首充返￥10', type: 'blue', emoji: '' }
						],
						feedback: {
							highlight: '今日放水1.5元',
							content: '，看剧10分钟就能提现，非常良心！'
						},
						submitter: {
							name: '用户7621',
							device: 'OPPO Find X5'
						}
					},
					{
						id: 4,
						appName: '阅读赚',
						type: 'book',
						rating: 4.6,
						downloadCount: 2120,
						submitTime: '2025-05-11 09:33',
						tags: [
							{ text: '稳定', type: 'red', emoji: '🔥' },
							{ text: '$1起提', type: 'amber', emoji: '⚡' },
							{ text: '新人$0.5', type: 'green', emoji: '' },
							{ text: '签到￥0.5', type: 'blue', emoji: '' }
						],
						feedback: {
							highlight: '连续签到奖励0.6元',
							content: '，今天阅读文章特别多，收益高！'
						},
						submitter: {
							name: '用户1934',
							device: 'vivo X90'
						}
					}
				]
			}
		},
		methods: {
			// 切换标签
			switchTab(index) {
				this.activeTab = index
				this.loadMoreData(true)
			},
			
			// 下拉刷新
			onRefresh() {
				this.isRefreshing = true
				setTimeout(() => {
					this.currentPage = 1
					this.loadMoreData(true)
					this.isRefreshing = false
				}, 1500)
			},
			
			// 刷新恢复
			onRestore() {
				this.isRefreshing = false
			},
			
			// 上滑加载更多
			onLoadMore() {
				if (this.loadStatus !== 'loading') {
					this.loadMoreData()
				}
			},
			
			// 加载更多数据
			loadMoreData(isRefresh = false) {
				this.loadStatus = 'loading'
				this.showLoadMore = true

				// 如果是刷新，重置页码
				if (isRefresh) {
					this.currentPage = 1
				} else {
					// 非刷新情况下，先递增页码
					this.currentPage++
				}

				// 构建请求参数
				const params = {
					page: this.currentPage,
					page_size: this.pageSize
				}

				// 如果有搜索关键词，添加搜索条件
				if (this.searchValue.trim()) {
					params.search = this.searchValue.trim()
				}

				// 调用真实API
				uni.$u.http.get('/clue/list', {params: params}).then(res => {
					console.log('线索列表API返回数据:', res)

					if (isRefresh) {
						this.clueList = res.list || []
					} else {
						this.clueList = [...this.clueList, ...(res.list || [])]
					}

					// 更新分页状态
					if (res.has_more) {
						this.loadStatus = 'loadmore'
						this.showLoadMore = true
					} else {
						this.loadStatus = 'nomore'
						// 如果有数据则显示"没有更多了"，否则隐藏
						this.showLoadMore = this.clueList.length > 0
					}
					console.log('当前页码:', this.currentPage, '加载状态:', this.loadStatus);

				}).catch(err => {
					console.error('加载线索数据失败:', err)
					// 请求失败时恢复页码
					if (!isRefresh) {
						this.currentPage--
					}
					this.loadStatus = 'loadmore'
					this.showLoadMore = this.clueList.length > 0
					uni.showToast({
						title: '加载失败，请重试',
						icon: 'none'
					})
				})
			},
			
			// 获取卡片渐变样式
			getCardGradientClass(index) {
				const gradients = ['gradient-red', 'gradient-blue', 'gradient-green', 'gradient-amber']
				return gradients[index % 4]
			},
			
			// 获取图标样式
			getIconClass(type) {
				const iconClasses = {
					coin: 'icon-coin',
					video: 'icon-video', 
					cash: 'icon-cash',
					book: 'icon-book'
				}
				return iconClasses[type] || 'icon-coin'
			},
			
			// 获取图标符号
			getIconSymbol(type) {
				const symbols = {
					coin: '💰',
					video: '📺',
					cash: '💵',
					book: '📚'
				}
				return symbols[type] || '💰'
			},
			
			// 获取标签样式
			getTagClass(type) {
				return `tag-${type}`
			},
			
			// 线索卡片点击
			handleClueClick(item) {
				console.log('线索点击:', item.appName)
				// 跳转到详情页
			},
			
			// 下载按钮点击
			handleDownload(item) {
				uni.showToast({
					title: `下载${item.appName}`,
					icon: 'none'
				})
			},
			
			// 详情按钮点击
			handleDetail(item) {
				uni.showToast({
					title: `查看${item.appName}详情`,
					icon: 'none'
				})
			},
			
			// 提交线索
			handleSubmitClue() {
				uni.navigateTo({
					url: '/pages/submit-clue/submit-clue'
				})
			},

			// 处理搜索输入
			handleSearchInput(e) {
				// 如果搜索框被清空，恢复到非搜索状态
				if (!e.detail.value.trim()) {
					this.clearSearch()
				}
			},

			// 执行搜索
			handleSearch() {
				const keyword = this.searchValue.trim()
				if (!keyword) {
					this.clearSearch()
					return
				}

				this.isSearching = true
				this.searchKeyword = keyword
				this.currentPage = 1
				this.loadMoreData(true)
			},

			// 清空搜索
			clearSearch() {
				this.isSearching = false
				this.searchKeyword = ''
				this.searchValue = ''
				this.currentPage = 1
				this.loadMoreData(true)
			}
		},
		
		onLoad() {
			// 初始加载
			this.loadMoreData(true)
		}
	}
</script>

<style lang="scss" scoped>
.page-container {
	width: 100%;
	height: 100vh;
	background-color: #f5f5f5;
}

/* 固定顶部区域 */
.fixed-header {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background-color: #ffffff;
	border-bottom: 2rpx solid #f0f0f0;
	padding: 24rpx 0 16rpx;
	
	/* 搜索框区域 */
	.search-section {
		padding: 0 32rpx;
		margin-bottom: 16rpx;
		
		.search-container {
			position: relative;
			width: 100%;
			
			.search-input {
				background-color: #f3f4f6;
				border-radius: 50rpx;
				padding: 16rpx 80rpx 16rpx 32rpx;
				font-size: 28rpx;
				border: none;
				outline: none;
				color: #111827;
				width: 100%;
				box-sizing: border-box;
				min-height: 80rpx;
				
				&::placeholder {
					color: #9ca3af;
				}
				
				&:focus {
					outline: none;
					border: none;
				}
			}
			
			.search-icon, .clear-icon {
				position: absolute;
				right: 24rpx;
				top: 50%;
				transform: translateY(-50%);
				cursor: pointer;

				.icon-search {
					width: 32rpx;
					height: 32rpx;
					background-color: #6b7280;
					mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5l-1.5 1.5l-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16A6.5 6.5 0 0 1 3 9.5A6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14S14 12 14 9.5S12 5 9.5 5Z'/%3E%3C/svg%3E") no-repeat center;
					mask-size: contain;
				}

				.icon-clear {
					width: 32rpx;
					height: 32rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					background-color: #9ca3af;
					border-radius: 50%;
					color: #ffffff;
					font-size: 24rpx;
					font-weight: bold;
				}
			}
		}
	}
	
	/* 标签导航区域 */
	.tab-section {
		padding: 0 32rpx 16rpx;
		
		.tab-container {
			display: flex;
			
			.tab-item {
				margin-right: 48rpx;
				padding-bottom: 16rpx;
				position: relative;
				
				.tab-text {
					font-size: 32rpx;
					color: #6b7280;
					font-weight: 500;
				}
				
				&.active {
					.tab-text {
						color: #111827;
						font-weight: bold;
					}
					
					&::after {
						content: '';
						position: absolute;
						bottom: 0;
						left: 0;
						right: 0;
						height: 4rpx;
						background-color: #ef4444;
						border-radius: 2rpx;
					}
				}
			}
		}
	}
}

/* 内容区域 */
.content-container {
	padding-top: 200rpx; /* 为固定顶部留出空间 */
	height: 100vh;
	background-color: #f9fafb;
	
	.scroll-container {
		height: 100%;
		
		.content-inner {
			padding: 32rpx;

			/* 标题样式 */
			.section-title {
				margin-bottom: 24rpx;

				.title-text {
					font-size: 36rpx; /* text-lg */
					font-weight: bold;
					color: #111827;
				}
			}

			/* 提交按钮区域 */
			.submit-section {
				position: sticky;
				top: 0;
				z-index: 10;
				background-color: #f9fafb;
				padding: 24rpx 0;
				margin-bottom: 32rpx;
				
				.submit-btn {
					width: 100%;
					background: linear-gradient(135deg, #dc2626, #b91c1c);
					border-radius: 16rpx;
					padding: 18rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					border: none;
					box-shadow: 0 4rpx 12rpx rgba(220, 38, 38, 0.3);
					
					.submit-icon {
						font-size: 32rpx;
						color: #ffffff;
						margin-right: 12rpx;
						font-weight: bold;
					}
					
					.submit-text {
						font-size: 28rpx;
						color: #ffffff;
						font-weight: 600;
					}
				}
			}
			
			/* 时间线容器 */
			.timeline-container {
				position: relative;
				
				.timeline-item {
					display: flex;
					margin-bottom: 48rpx;
					
					/* 时间线左侧 */
					.timeline-left {
						display: flex;
						flex-direction: column;
						align-items: center;
						margin-right: 32rpx;
						
						.timeline-icon {
							width: 80rpx;
							height: 80rpx;
							background: linear-gradient(135deg, #fef2f2, #fee2e2);
							border-radius: 50%;
							display: flex;
							align-items: center;
							justify-content: center;
							border: 4rpx solid #ffffff;
							box-shadow: 0 4rpx 12rpx rgba(220, 38, 38, 0.2);
							
							.water-icon {
								font-size: 32rpx;
							}
						}
						
						.timeline-line {
							width: 2rpx;
							flex: 1;
							background-color: #e5e7eb;
							margin-top: 16rpx;
							min-height: 100rpx;
						}
					}
					
					/* 时间线右侧 */
					.timeline-right {
						flex: 1;
						
						.timeline-time {
							margin-bottom: 16rpx;
							
							.time-text {
								font-size: 24rpx;
								color: #9ca3af;
							}
						}
						
						/* 线索卡片 */
						.clue-card {
							background: #ffffff;
							border-radius: 16rpx;
							padding: 32rpx;
							box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.1);
							position: relative;
							overflow: hidden;
							
							/* 渐变背景 */
							&.gradient-red {
								background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
							}
							
							&.gradient-blue {
								background: linear-gradient(135deg, #eff6ff 0%, #ffffff 100%);
							}
							
							&.gradient-green {
								background: linear-gradient(135deg, #f0fdf4 0%, #ffffff 100%);
							}
							
							&.gradient-amber {
								background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
							}
							
							/* 卡片头部 */
							.card-header {
								display: flex;
								justify-content: space-between;
								align-items: center;
								margin-bottom: 24rpx;
								
								.app-info {
									display: flex;
									align-items: center;
									
									.app-icon {
										width: 48rpx;
										height: 48rpx;
										border-radius: 8rpx;
										display: flex;
										align-items: center;
										justify-content: center;
										margin-right: 16rpx;
										
										&.icon-coin {
											background-color: #f59e0b;
										}
										
										&.icon-video {
											background-color: #ef4444;
										}
										
										&.icon-cash {
											background-color: #10b981;
										}
										
										&.icon-book {
											background-color: #3b82f6;
										}
										
										.icon-symbol {
											font-size: 22rpx;
										}
									}
									
									.app-name {
										font-size: 32rpx;
										font-weight: 600;
										color: #111827;
									}
								}
								
								.app-stats {
									display: flex;
									gap: 32rpx;
									
									.rating, .download-count {
										display: flex;
										align-items: center;
										
										.star-icon, .download-icon {
											font-size: 20rpx;
											margin-right: 4rpx;
										}
										
										.rating-text, .count-text {
											font-size: 24rpx;
											color: #6b7280;
										}
									}
									
									.rating .rating-text {
										color: #f59e0b;
									}
								}
							}
							
							/* 标签区域 */
							.tags-container {
								display: flex;
								flex-wrap: wrap;
								gap: 16rpx;
								margin-bottom: 24rpx;
								
								.tag {
									border-radius: 24rpx;
									padding: 4rpx 12rpx;
									display: inline-flex;
									align-items: center;
									justify-content: center;
									min-height: 44rpx;
									
									.tag-text {
										font-size: 22rpx;
										font-weight: 500;
										line-height: 1;
									}
									
									&.tag-blue {
										background-color: #dbeafe;
										
										.tag-text {
											color: #2563eb;
										}
									}
									
									&.tag-gray {
										background-color: #f3f4f6;
										
										.tag-text {
											color: #4b5563;
										}
									}
									
									&.tag-green {
										background-color: #dcfce7;
										
										.tag-text {
											color: #16a34a;
										}
									}
									
									&.tag-red {
										background-color: #fee2e2;
										
										.tag-text {
											color: #dc2626;
										}
									}
									
									&.tag-amber {
										background-color: #fef3c7;
										
										.tag-text {
											color: #d97706;
										}
									}
								}
							}
							
							/* 用户反馈区域 */
							.feedback-section {
								background-color: #f9fafb;
								border-radius: 12rpx;
								padding: 24rpx;
								margin-bottom: 24rpx;
								
								.feedback-label {
									font-size: 28rpx;
									color: #374151;
								}
								
								.feedback-highlight {
									font-size: 28rpx;
									color: #ef4444;
									font-weight: 600;
								}
								
								.feedback-content {
									font-size: 28rpx;
									color: #374151;
								}
								
								.feedback-footer {
									margin-top: 16rpx;
									
									.submitter-info {
										font-size: 22rpx;
										color: #9ca3af;
									}
								}
							}
							
							/* 卡片底部 */
							.card-footer {
								display: flex;
								justify-content: space-between;
								align-items: center;
								
								.action-btn {
									background: linear-gradient(135deg, #ef4444, #dc2626);
									border-radius: 24rpx;
									padding: 12rpx 24rpx;
									
									.btn-text {
										color: #ffffff;
										font-size: 28rpx;
										font-weight: 600;
									}
								}
								
								.detail-btn {
									display: flex;
									align-items: center;
									
									.detail-text {
										color: #ef4444;
										font-size: 28rpx;
										font-weight: 600;
										margin-right: 8rpx;
									}
									
									.arrow {
										color: #ef4444;
										font-size: 32rpx;
										font-weight: bold;
									}
								}
							}
						}
					}
				}
			}
			
			/* 加载更多容器 */
			.load-more-container {
				margin: 32rpx 0;
				padding-bottom: 120rpx; /* 为底部导航留出空间 */
			}
		}
	}
}

/* 隐藏滚动条 */
::-webkit-scrollbar {
	display: none;
}
</style>
